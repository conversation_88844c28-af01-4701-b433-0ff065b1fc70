# Refactoring Plan - MyDSitefinityAPI Cleanup

## Current Tasks

### 1. Remove Redundant Controllers and Methods
**Risk Level: Low**
**Status: Pending**

**Current Issue:**
- `PostRecruitmentForm` method duplicates functionality already in main `PostForm` method
- `VacancyController` provides redundant recruitment functionality
- Multiple endpoints for same business logic create confusion and maintenance overhead

**Files to Remove:**
- `MyDSitefinityAPI\Controllers\VacancyController.cs`
- `MyDSitefinityAPI\Controllers\SitefinityFormController.cs` - Remove `PostRecruitmentForm` method only

**Services to Evaluate for Removal:**
- `IVacancyService` and `VacancyService` (if only used by VacancyController)
- `IVacancyDbService` and `VacancyDbService` (check usage in SitefinityFormService)

**Files to Update:**
- `MyDSitefinityAPI\Program.cs` (remove DI registrations for removed services)
- `MyDSitefinityAPI\Services\SitefinityFormService.cs` (remove unused dependencies)

### 2. Dependency Cleanup
**Risk Level: Low**
**Status: Pending**

**Current Issue:**
- Unused EntityFramework NuGet packages in multiple projects

**Files to Update:**
- Remove unused `EntityFramework` NuGet package from `MyDSitefinityAPI.csproj`
- Remove unused `EntityFramework` NuGet package from `MyDSitefinityAPI.Persistence.csproj`

### 3. Security Enhancement
**Risk Level: Medium**
**Status: Pending**

**Current Issue:**
- Hardcoded credentials and URLs in `RecruitmentHubService.cs`

**Files to Update:**
- Move hardcoded credentials and URLs from `RecruitmentHubService.cs` to `appsettings.json`
- Update code to read these values from configuration

### 4. Configuration Modernization
**Risk Level: Medium**
**Status: Pending**

**Current Issue:**
- Using legacy app.config instead of modern appsettings.json for configuration

**Files to Update:**
- Migrate configuration from `app.config` to `appsettings.json`
- Update code to use IConfiguration instead of ConfigurationManager

### 5. Implement NLog Structured Logging
**Risk Level: Medium**
**Status: Pending**

**Current Issue:**
- Using basic file logging with limited structure and filtering
- No proper request/response tracking
- Entity Framework and Microsoft framework noise in logs
- Hardcoded log file paths in configuration

**Files to Update:**
- `MyDSitefinityAPI\Program.cs` - Replace current logging setup with NLog configuration
- `MyDSitefinityAPI\Services\SitefinityFormService.cs` - Update logging calls to use structured logging
- `MyDSitefinityAPI\Controllers\SitefinityFormController.cs` - Add structured logging for requests/responses
- `MyDSitefinityAPI\MyDSitefinityAPI.csproj` - Add NLog.Web.AspNetCore NuGet package
- `MyDSitefinityAPI\NLog.config` - Create NLog configuration file
- `MyDSitefinityAPI\appsettings.json` - Remove LogLocation configuration

**NLog Configuration Requirements:**
- Log directory: `c:\logs\FormSubmission.API-${environment}\`
- Two log targets: all logs + application-specific with request URLs/MVC actions
- Filter out Entity Framework command logs and Microsoft framework noise
- Use structured logging format for better parsing

**Program.cs Changes:**
```csharp
// Early init of NLog to allow startup and exception logging, before host is built
var logger = NLog.LogManager.Setup().LoadConfigurationFromAppSettings().GetCurrentClassLogger();
logger.Debug("init main");

// NLog: Setup NLog for Dependency injection
builder.Logging.ClearProviders();
builder.Logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Warning);
builder.Logging.AddFilter("Microsoft", Microsoft.Extensions.Logging.LogLevel.Warning);
builder.Logging.AddFilter("Microsoft.EntityFrameworkCore", Microsoft.Extensions.Logging.LogLevel.Warning);
builder.Logging.AddFilter("Microsoft.EntityFrameworkCore.Database.Command", Microsoft.Extensions.Logging.LogLevel.Warning);
builder.Host.UseNLog();
```

**Benefits:**
- Better structured logging for debugging and monitoring
- Proper request/response tracking
- Reduced log noise from framework components
- Consistent logging format across applications
- Environment-specific log directories

### 6. Optimize Logging - Log Only on Failures
**Risk Level: Low**
**Status: Pending**

**Current Issue:**
- Currently logging every form submission request, creating excessive log volume
- Successful form submissions don't need detailed logging for debugging
- Raw JSON logging happens on every request when RawJsonLogging=yes
- Log files become large and difficult to analyze due to noise from successful operations

**Files to Update:**
- `MyDSitefinityAPI\Controllers\SitefinityFormController.cs` - Remove success logging, keep only failure/error logging
- `MyDSitefinityAPI\Services\SitefinityFormService.cs` - Remove success logging from all form processing methods
- Keep error logging and warnings for debugging failed submissions

**Optimization Strategy:**
- Remove "Form submission received" and "Processing form submission" info logs
- Remove "Successfully saved" info logs from all form processing methods
- Keep all error logging (LogError, LogWarning) for troubleshooting
- Keep raw JSON logging only when processing fails (move to catch blocks)
- Maintain structured logging format for remaining error logs

**Benefits:**
- Significantly reduced log file sizes
- Faster log analysis focused on actual issues
- Reduced I/O overhead from excessive logging
- Cleaner logs for production monitoring
- Easier identification of real problems

## Implementation Steps

### Step 1: Remove VacancyController
- Delete entire `MyDSitefinityAPI\Controllers\VacancyController.cs` file

### Step 2: Remove PostRecruitmentForm Method
- Edit `MyDSitefinityAPI\Controllers\SitefinityFormController.cs`
- Remove the `PostRecruitmentForm` method (keep main `PostForm` method)

### Step 3: Clean Up Services
- Check if `IVacancyService`/`VacancyService` are only used by VacancyController
- Check if `IVacancyDbService`/`VacancyDbService` are used in SitefinityFormService
- Remove unused service registrations from `Program.cs`

### Step 4: Update Dependencies
- Remove unused service dependencies from SitefinityFormService constructor

### Step 5: Remove Unused NuGet Packages
- Remove EntityFramework package from both projects

### Step 6: Security Configuration
- Move hardcoded values to appsettings.json
- Update RecruitmentHubService to use IConfiguration

### Step 7: Configuration Migration
- Migrate app.config settings to appsettings.json
- Update all services to use IConfiguration

### Step 5: Implement NLog Structured Logging
- Add NLog.Web.AspNetCore NuGet package
- Create NLog.config file with proper targets and rules
- Update Program.cs to use NLog instead of current file logging
- Replace logging calls in SitefinityFormService.cs with structured logging
- Add request/response logging to controllers
- Remove LogLocation from appsettings.json
- Test logging functionality across all endpoints

## Verification Steps
- [ ] Confirm `sf_recruitmentjobapplication` still works through main `PostForm` endpoint
- [ ] Ensure no external systems call the removed endpoints
- [ ] Run integration tests
- [ ] Verify all configuration values are properly migrated
- [ ] Test security enhancements don't break functionality

## Completed Tasks
(None yet)

## Notes
- Main `PostForm` method already handles `sf_recruitmentjobapplication` forms via case statement
- Need to verify if `IVacancyDbService` is used elsewhere before removing
- The JSON sample shows `sf_recruitmentjobapplication` form structure - this should continue working through main endpoint
- Follow SOLID principles and existing project conventions
- Compile and test after each step

**Test Coverage Verification (BEFORE making changes):**
- [x] Created test for main `PostForm` method with `sf_recruitmentjobapplication` payload
- [ ] Run new test to verify recruitment form processing works through main endpoint
- [ ] Run full test suite to establish baseline
- [ ] Document current test coverage metrics
- [ ] Verify `VacancyControllerTests.ApplyToVacancy` functionality is covered by new test

**Test Files to Remove:**
- `MyDSitefinityAPI.IntegrationTests\Tests\Controllers\VacancyControllerTests.cs` (entire file)
- Any unit tests for `VacancyService` and `VacancyDbService` (if they exist)
- Any tests specifically for `PostRecruitmentForm` method

**New Test Files Created:**
- Added `PostRecruitmentJobApplicationForm` test to `SitefinityFormControllerTests.cs`
- Created `GoldenMasters/RecruitmentJobApplication.json` with real recruitment payload

