<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="c:\temp\internal-nlog-AspNetCore.txt">

  <!-- enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
  </extensions>

  <!-- the targets to write to -->
  <targets>
    <!-- File Target for all log messages with basic details -->
    <target xsi:type="File" name="allfile" fileName="c:\logs\FormSubmission.API-${environment:ASPNETCORE_ENVIRONMENT:whenEmpty=Development}\nlog-AspNetCore-all-${shortdate}.log"
            layout="${longdate}|${event-properties:item=EventId:whenEmpty=0}|${level:uppercase=true}|${logger}|${message} ${exception:format=tostring}" />

    <!-- File Target for application-specific logs with request details -->
    <target xsi:type="File" name="ownFile-web" fileName="c:\logs\FormSubmission.API-${environment:ASPNETCORE_ENVIRONMENT:whenEmpty=Development}\nlog-AspNetCore-own-${shortdate}.log"
            layout="${longdate}|${event-properties:item=EventId:whenEmpty=0}|${level:uppercase=true}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}|${callsite}" />

    <!--Console Target for hosting lifetime messages to improve Docker / Visual Studio startup detection -->
    <target xsi:type="Console" name="lifetimeConsole" layout="${MicrosoftConsoleLayout}" />
  </targets>

  <!-- rules to map from logger name to target -->
  <rules>
    <!--Output hosting lifetime messages to console target for faster startup detection -->
    <logger name="Microsoft.Hosting.Lifetime" minlevel="Info" writeTo="lifetimeConsole, ownFile-web" final="true" />

    <!--Skip non-critical Microsoft logs and so log only own logs (BlackHole) -->
    <logger name="Microsoft.AspNetCore.Server.Kestrel.*" maxlevel="Info" final="true" />
    <logger name="Microsoft.AspNetCore.Hosting.*" maxlevel="Info" final="true" />
    <logger name="Microsoft.AspNetCore.Routing.*" maxlevel="Info" final="true" />
    <logger name="Microsoft.*" maxlevel="Info" final="true" />
    <logger name="System.Net.Http.*" maxlevel="Info" final="true" />
    <logger name="Microsoft.EntityFrameworkCore.*" maxlevel="Warning" final="true" />

    <!--All logs, including from Microsoft (but only Info and above to avoid excessive TRACE/DEBUG)-->
    <logger name="*" minlevel="Info" writeTo="allfile" />

    <!--Application specific logs (your application logs can still be at Debug level)-->
    <logger name="MyDSitefinityAPI.*" minlevel="Debug" writeTo="ownFile-web" />
    <logger name="Mydentist.*" minlevel="Debug" writeTo="ownFile-web" />
  </rules>
</nlog>
